#!/usr/bin/env node

/**
 * Environment Check Script
 * 检查开发环境是否正确配置
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

function checkCommand(command, name) {
  try {
    const version = execSync(command, { encoding: 'utf8', stdio: 'pipe' }).trim();
    console.log(`✅ ${name}: ${version}`);
    return true;
  } catch (error) {
    console.log(`❌ ${name}: 未安装或不可用`);
    return false;
  }
}

function checkFile(filePath, name) {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${name}: 存在`);
    return true;
  } else {
    console.log(`❌ ${name}: 不存在`);
    return false;
  }
}

function checkPackageJson() {
  const packagePath = path.join(__dirname, 'package.json');
  if (!fs.existsSync(packagePath)) {
    console.log('❌ package.json: 不存在');
    return false;
  }
  
  try {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    console.log(`✅ package.json: 存在`);
    console.log(`   应用名称: ${packageJson.name}`);
    console.log(`   版本: ${packageJson.version}`);
    console.log(`   描述: ${packageJson.description}`);
    
    // 检查必要的依赖
    const requiredDeps = ['electron', 'electron-builder'];
    const requiredRuntimeDeps = ['electron-updater'];
    
    let depsOk = true;
    
    console.log('\n📦 开发依赖检查:');
    for (const dep of requiredDeps) {
      if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
        console.log(`   ✅ ${dep}: ${packageJson.devDependencies[dep]}`);
      } else {
        console.log(`   ❌ ${dep}: 未安装`);
        depsOk = false;
      }
    }
    
    console.log('\n📦 运行时依赖检查:');
    for (const dep of requiredRuntimeDeps) {
      if (packageJson.dependencies && packageJson.dependencies[dep]) {
        console.log(`   ✅ ${dep}: ${packageJson.dependencies[dep]}`);
      } else {
        console.log(`   ❌ ${dep}: 未安装`);
        depsOk = false;
      }
    }
    
    return depsOk;
  } catch (error) {
    console.log(`❌ package.json: 格式错误 - ${error.message}`);
    return false;
  }
}

function checkIcons() {
  const iconsPath = path.join(__dirname, 'assets');
  const iconFiles = ['icon.png', 'icon.ico', 'icon.icns'];
  
  console.log('\n🎨 图标文件检查:');
  let iconsOk = true;
  
  if (!fs.existsSync(iconsPath)) {
    console.log('❌ assets 文件夹不存在');
    return false;
  }
  
  for (const iconFile of iconFiles) {
    const iconPath = path.join(iconsPath, iconFile);
    if (fs.existsSync(iconPath)) {
      const stats = fs.statSync(iconPath);
      console.log(`   ✅ ${iconFile}: 存在 (${Math.round(stats.size / 1024)}KB)`);
    } else {
      console.log(`   ⚠️  ${iconFile}: 不存在 (可选，但推荐添加)`);
      if (iconFile === 'icon.png') {
        iconsOk = false; // PNG 图标是必需的
      }
    }
  }
  
  return iconsOk;
}

function main() {
  console.log('🔍 Electron 应用环境检查\n');
  
  console.log('🛠️  系统工具检查:');
  const nodeOk = checkCommand('node --version', 'Node.js');
  const npmOk = checkCommand('npm --version', 'npm');
  const gitOk = checkCommand('git --version', 'Git');
  
  console.log('\n📁 项目文件检查:');
  const packageOk = checkPackageJson();
  const mainFileOk = checkFile(path.join(__dirname, 'src', 'main.js'), 'src/main.js');
  const htmlFileOk = checkFile(path.join(__dirname, 'src', 'index.html'), 'src/index.html');
  const rendererFileOk = checkFile(path.join(__dirname, 'src', 'renderer.js'), 'src/renderer.js');
  const workflowOk = checkFile(path.join(__dirname, '.github', 'workflows', 'build.yml'), 'GitHub Actions 配置');
  
  const iconsOk = checkIcons();
  
  console.log('\n📋 总结:');
  const allChecks = [
    { name: 'Node.js', status: nodeOk },
    { name: 'npm', status: npmOk },
    { name: 'Git', status: gitOk },
    { name: 'package.json', status: packageOk },
    { name: '主要文件', status: mainFileOk && htmlFileOk && rendererFileOk },
    { name: 'GitHub Actions', status: workflowOk },
    { name: '图标文件', status: iconsOk }
  ];
  
  const passedChecks = allChecks.filter(check => check.status).length;
  const totalChecks = allChecks.length;
  
  console.log(`通过检查: ${passedChecks}/${totalChecks}`);
  
  if (passedChecks === totalChecks) {
    console.log('\n🎉 恭喜！你的开发环境已经准备就绪！');
    console.log('\n🚀 下一步操作:');
    console.log('1. 运行 "npm install" 安装依赖');
    console.log('2. 运行 "npm run dev" 启动开发版本');
    console.log('3. 运行 "npm run build" 测试构建');
  } else {
    console.log('\n⚠️  请解决上述问题后再继续开发');
    console.log('\n💡 建议:');
    if (!nodeOk) console.log('- 安装 Node.js: https://nodejs.org/');
    if (!gitOk) console.log('- 安装 Git: https://git-scm.com/');
    if (!packageOk) console.log('- 运行 "npm install" 安装依赖');
    if (!iconsOk) console.log('- 在 assets 文件夹中添加应用图标');
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
