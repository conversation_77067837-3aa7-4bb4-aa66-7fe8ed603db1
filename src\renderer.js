const { ipc<PERSON>ender<PERSON> } = require('electron');

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', async () => {
    // 获取并显示应用版本
    try {
        const version = await ipcRenderer.invoke('get-app-version');
        document.getElementById('version').textContent = `版本: v${version}`;
    } catch (error) {
        console.error('获取版本失败:', error);
        document.getElementById('version').textContent = '版本: 未知';
    }
});

// 检查更新
async function checkForUpdates() {
    const statusElement = document.getElementById('status');
    statusElement.textContent = '正在检查更新...';
    
    try {
        const result = await ipcRenderer.invoke('check-for-updates');
        if (result) {
            statusElement.textContent = '正在检查更新，请稍候...';
        } else {
            statusElement.textContent = '开发模式下无法检查更新';
        }
    } catch (error) {
        console.error('检查更新失败:', error);
        statusElement.textContent = '检查更新失败';
    }
}

// 显示关于信息
function showAbout() {
    const statusElement = document.getElementById('status');
    statusElement.innerHTML = `
        <strong>关于此应用</strong><br>
        这是一个使用 Electron 构建的跨平台桌面应用程序。<br>
        支持 Windows、macOS 和 Linux 系统。<br>
        具有自动更新功能，可以自动获取最新版本。
    `;
}

// 打开开发者工具
function openDevTools() {
    // 这个功能需要在主进程中实现
    const statusElement = document.getElementById('status');
    statusElement.textContent = '开发者工具已在主进程中配置';
}

// 监听键盘快捷键
document.addEventListener('keydown', (event) => {
    // Ctrl+R 或 Cmd+R 刷新页面
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        location.reload();
    }
    
    // F12 打开开发者工具（在开发模式下）
    if (event.key === 'F12') {
        event.preventDefault();
        openDevTools();
    }
});

// 添加一些交互效果
document.querySelectorAll('button').forEach(button => {
    button.addEventListener('mouseenter', () => {
        button.style.transform = 'translateY(-2px)';
    });
    
    button.addEventListener('mouseleave', () => {
        button.style.transform = 'translateY(0)';
    });
});
