# AI重器

一个跨平台的Electron桌面应用程序，支持自动更新功能。

## 🚀 功能特性

- ✅ 跨平台支持 (Windows, macOS, Linux)
- ✅ 自动更新功能
- ✅ GitHub Actions 自动构建和发布
- ✅ 现代化的用户界面
- ✅ 开发者友好的配置

## 📦 安装和运行

### 开发环境

1. 克隆项目
```bash
git clone https://github.com/yourusername/my-electron-app.git
cd my-electron-app
```

2. 安装依赖
```bash
npm install
```

3. 运行开发版本
```bash
npm run dev
```

### 构建应用

```bash
# 构建所有平台
npm run build

# 构建特定平台
npm run build:win    # Windows
npm run build:mac    # macOS
npm run build:linux  # Linux
```

## 🔧 配置说明

### 1. 修改应用信息

编辑 `package.json` 文件中的以下字段：

```json
{
  "name": "your-app-name",
  "version": "1.0.0",
  "description": "你的应用描述",
  "homepage": "https://github.com/yourusername/your-app-name",
  "author": {
    "name": "Your Name",
    "email": "<EMAIL>"
  },
  "build": {
    "appId": "com.yourcompany.yourappname",
    "productName": "Your App Name",
    "publish": {
      "provider": "github",
      "owner": "yourusername",
      "repo": "your-app-name"
    }
  }
}
```

### 2. 添加应用图标

在 `assets` 文件夹中放置以下图标文件：
- `icon.png` (512x512 像素，用于 Linux)
- `icon.ico` (包含多种尺寸，用于 Windows)
- `icon.icns` (包含多种尺寸，用于 macOS)

可以使用在线工具转换图标格式：
- [PNG to ICO](https://convertio.co/png-ico/)
- [PNG to ICNS](https://convertio.co/png-icns/)

## 🚀 GitHub Actions 自动构建

### 设置步骤

1. **创建 GitHub 仓库**
   - 在 GitHub 上创建新仓库
   - 将代码推送到仓库

2. **配置仓库设置**
   - 进入仓库的 Settings → Actions → General
   - 确保 "Allow GitHub Actions to create and approve pull requests" 已启用

3. **推送代码触发构建**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

4. **创建发布版本**
   ```bash
   # 创建并推送标签来触发发布
   git tag v1.0.0
   git push origin v1.0.0
   ```

### 自动构建流程

- **推送到 main 分支**: 触发构建，生成构建产物
- **推送标签 (v*)**: 触发构建并自动创建 GitHub Release

## 🔄 自动更新功能

应用内置了自动更新功能：

1. **自动检查**: 应用启动时自动检查更新
2. **手动检查**: 点击"检查更新"按钮
3. **自动下载**: 发现新版本时自动下载
4. **提示安装**: 下载完成后提示用户安装

### 更新流程

1. 修改 `package.json` 中的版本号
2. 提交代码并推送
3. 创建新的版本标签
4. GitHub Actions 自动构建并发布
5. 用户的应用会自动检测到新版本

## 📝 版本发布流程

1. **更新版本号**
   ```bash
   npm version patch  # 补丁版本 (1.0.0 -> 1.0.1)
   npm version minor  # 次要版本 (1.0.0 -> 1.1.0)
   npm version major  # 主要版本 (1.0.0 -> 2.0.0)
   ```

2. **推送标签**
   ```bash
   git push origin main --tags
   ```

3. **等待自动构建**
   - GitHub Actions 会自动构建所有平台的安装包
   - 构建完成后会自动创建 GitHub Release

## 🛠️ 开发指南

### 项目结构

```
my-electron-app/
├── src/
│   ├── main.js          # 主进程文件
│   ├── index.html       # 渲染进程页面
│   └── renderer.js      # 渲染进程脚本
├── assets/              # 应用图标和资源
├── .github/workflows/   # GitHub Actions 配置
├── package.json         # 项目配置
└── README.md           # 说明文档
```

### 添加新功能

1. 修改 `src/index.html` 添加 UI 元素
2. 在 `src/renderer.js` 中添加前端逻辑
3. 在 `src/main.js` 中添加主进程逻辑
4. 使用 IPC 通信连接前端和后端

## 🐛 常见问题

### 1. 构建失败
- 检查 Node.js 版本 (推荐 18+)
- 确保所有依赖已正确安装
- 检查 GitHub Actions 日志

### 2. 自动更新不工作
- 确保应用已发布到 GitHub Releases
- 检查 `package.json` 中的 `publish` 配置
- 确保版本号格式正确 (v1.0.0)

### 3. 图标不显示
- 确保图标文件存在于 `assets` 文件夹
- 检查图标文件格式和尺寸
- 重新构建应用

## 📄 许可证

MIT License