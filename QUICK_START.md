# ⚡ 快速开始指南

## 🎯 5分钟快速上手

### 第1步：检查环境 (1分钟)
```bash
npm run check
```
如果有红色❌，请先安装缺失的工具。

### 第2步：配置应用 (2分钟)
```bash
npm run setup
```
按提示输入你的应用信息。

### 第3步：安装依赖 (1分钟)
```bash
npm install
```

### 第4步：创建图标 (可选)
```bash
npm run create-icon
```
然后在浏览器中打开 `assets/icon-preview.html` 保存图标。

### 第5步：运行应用 (1分钟)
```bash
npm run dev
```

🎉 **恭喜！你的Electron应用已经运行起来了！**

---

## 🚀 发布到GitHub

### 1. 创建GitHub仓库
1. 访问 [GitHub](https://github.com)
2. 点击 "New repository"
3. 填写仓库名称
4. 选择 "Public"
5. 点击 "Create repository"

### 2. 推送代码
```bash
git init
git add .
git commit -m "Initial commit"
git branch -M main
git remote add origin https://github.com/你的用户名/你的仓库名.git
git push -u origin main
```

### 3. 创建第一个发布版本
```bash
git tag v1.0.0
git push origin v1.0.0
```

### 4. 等待自动构建
- 访问你的GitHub仓库
- 点击 "Actions" 查看构建进度
- 构建完成后在 "Releases" 页面下载安装包

---

## 📝 常用命令

| 命令 | 说明 |
|------|------|
| `npm run dev` | 启动开发版本 |
| `npm run build` | 构建所有平台 |
| `npm run build:win` | 只构建Windows版本 |
| `npm run build:mac` | 只构建macOS版本 |
| `npm run build:linux` | 只构建Linux版本 |
| `npm run check` | 检查开发环境 |
| `npm run setup` | 重新配置应用信息 |
| `npm run create-icon` | 创建示例图标 |

---

## 🔄 发布新版本流程

1. **修改代码**
2. **更新版本号**
   ```bash
   npm version patch  # 1.0.0 → 1.0.1
   ```
3. **推送更新**
   ```bash
   git push origin main --tags
   ```
4. **等待自动构建和发布**

---

## 🆘 遇到问题？

### 常见问题
- **构建失败**: 运行 `npm run check` 检查环境
- **图标不显示**: 确保 `assets` 文件夹中有图标文件
- **自动更新不工作**: 确保GitHub仓库是公开的

### 获取帮助
1. 查看 [详细使用指南](USAGE_GUIDE.md)
2. 检查 [README.md](README.md) 中的常见问题
3. 查看GitHub Actions的构建日志

---

## 🎨 自定义你的应用

### 修改界面
- 编辑 `src/index.html` - 页面结构和样式
- 编辑 `src/renderer.js` - 前端交互逻辑

### 添加功能
- 编辑 `src/main.js` - 主进程逻辑
- 使用IPC通信连接前端和后端

### 更换图标
1. 准备512x512的PNG图片
2. 使用在线工具转换为ICO和ICNS格式
3. 放入 `assets` 文件夹

---

## 🌟 下一步

现在你已经有了一个完整的Electron应用！你可以：

- ✅ 跨平台运行 (Windows, macOS, Linux)
- ✅ 自动构建和发布
- ✅ 自动更新功能
- ✅ 专业的项目结构

**开始构建你的梦想应用吧！** 🚀
