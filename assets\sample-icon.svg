<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆角矩形 -->
  <rect x="32" y="32" width="448" height="448" rx="80" ry="80" fill="url(#grad1)" />
  
  <!-- 主图标 - 电子符号 -->
  <circle cx="256" cy="200" r="20" fill="white" opacity="0.9"/>
  
  <!-- 电子轨道 -->
  <ellipse cx="256" cy="256" rx="120" ry="40" fill="none" stroke="white" stroke-width="8" opacity="0.7"/>
  <ellipse cx="256" cy="256" rx="40" ry="120" fill="none" stroke="white" stroke-width="8" opacity="0.7"/>
  <ellipse cx="256" cy="256" rx="85" ry="85" fill="none" stroke="white" stroke-width="6" opacity="0.5"/>
  
  <!-- 中心核心 -->
  <circle cx="256" cy="256" r="16" fill="white"/>
  
  <!-- 装饰性文字 -->
  <text x="256" y="380" font-family="Arial, sans-serif" font-size="48" font-weight="bold" text-anchor="middle" fill="white" opacity="0.8">E</text>
</svg>