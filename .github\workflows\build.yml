name: Build and Release

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ${{ matrix.os }}
    
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
        
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build application
      run: npm run build
      env:
        GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
    - name: Upload artifacts (Windows)
      if: matrix.os == 'windows-latest'
      uses: actions/upload-artifact@v4
      with:
        name: windows-build
        path: |
          dist/*.exe
          dist/*.msi
          dist/latest.yml
          
    - name: Upload artifacts (macOS)
      if: matrix.os == 'macos-latest'
      uses: actions/upload-artifact@v4
      with:
        name: macos-build
        path: |
          dist/*.dmg
          dist/*.zip
          dist/latest-mac.yml
          
    - name: Upload artifacts (Linux)
      if: matrix.os == 'ubuntu-latest'
      uses: actions/upload-artifact@v4
      with:
        name: linux-build
        path: |
          dist/*.AppImage
          dist/*.deb
          dist/latest-linux.yml

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download all artifacts
      uses: actions/download-artifact@v4
      
    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          windows-build/*
          macos-build/*
          linux-build/*
        draft: false
        prerelease: false
        generate_release_notes: true
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
