# 📚 Electron应用使用指南

## 🎯 快速开始（小白教程）

### 第一步：准备工作

1. **安装 Node.js**
   - 访问 [Node.js官网](https://nodejs.org/)
   - 下载并安装 LTS 版本（推荐18.x或更高版本）
   - 安装完成后，打开命令行输入 `node --version` 验证安装

2. **安装 Git**
   - 访问 [Git官网](https://git-scm.com/)
   - 下载并安装适合你系统的版本

3. **注册 GitHub 账号**
   - 访问 [GitHub](https://github.com/)
   - 注册一个免费账号

### 第二步：创建项目

1. **在 GitHub 上创建仓库**
   ```
   1. 登录 GitHub
   2. 点击右上角的 "+" 号
   3. 选择 "New repository"
   4. 填写仓库名称（例如：my-electron-app）
   5. 选择 "Public"（公开仓库才能使用免费的 GitHub Actions）
   6. 点击 "Create repository"
   ```

2. **克隆仓库到本地**
   ```bash
   git clone https://github.com/你的用户名/你的仓库名.git
   cd 你的仓库名
   ```

3. **复制项目文件**
   - 将我创建的所有文件复制到你的项目文件夹中

### 第三步：配置项目

1. **修改 package.json**
   ```json
   {
     "name": "你的应用名称",
     "description": "你的应用描述",
     "homepage": "https://github.com/你的用户名/你的仓库名",
     "author": {
       "name": "你的姓名",
       "email": "你的邮箱"
     },
     "build": {
       "appId": "com.你的公司名.你的应用名",
       "productName": "你的应用显示名称",
       "publish": {
         "provider": "github",
         "owner": "你的用户名",
         "repo": "你的仓库名"
       }
     }
   }
   ```

2. **添加应用图标**
   - 准备一个 512x512 像素的 PNG 图片作为应用图标
   - 使用在线工具转换为不同格式：
     - [PNG to ICO](https://convertio.co/png-ico/) - Windows图标
     - [PNG to ICNS](https://convertio.co/png-icns/) - macOS图标
   - 将文件放入 `assets` 文件夹：
     - `icon.png` - Linux图标
     - `icon.ico` - Windows图标
     - `icon.icns` - macOS图标

### 第四步：本地测试

1. **安装依赖**
   ```bash
   npm install
   ```

2. **运行开发版本**
   ```bash
   npm run dev
   ```

3. **测试构建**
   ```bash
   npm run build
   ```

### 第五步：推送到 GitHub

1. **提交代码**
   ```bash
   git add .
   git commit -m "Initial commit: Electron app setup"
   git push origin main
   ```

2. **检查 GitHub Actions**
   - 访问你的 GitHub 仓库
   - 点击 "Actions" 标签
   - 查看构建是否成功

### 第六步：发布第一个版本

1. **创建版本标签**
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```

2. **等待自动构建**
   - GitHub Actions 会自动构建所有平台的安装包
   - 构建完成后会在 "Releases" 页面创建新版本

3. **下载和测试**
   - 访问仓库的 "Releases" 页面
   - 下载适合你系统的安装包
   - 安装并测试自动更新功能

## 🔄 日常使用流程

### 发布新版本

1. **修改代码**
   - 编辑 `src/` 文件夹中的文件
   - 测试功能是否正常

2. **更新版本号**
   ```bash
   npm version patch  # 1.0.0 -> 1.0.1
   # 或者
   npm version minor  # 1.0.0 -> 1.1.0
   # 或者
   npm version major  # 1.0.0 -> 2.0.0
   ```

3. **推送更新**
   ```bash
   git push origin main --tags
   ```

4. **等待自动构建**
   - GitHub Actions 会自动构建并发布新版本
   - 用户的应用会自动检测到更新

### 自定义应用

1. **修改界面**
   - 编辑 `src/index.html` 修改页面内容
   - 编辑 `src/renderer.js` 添加交互功能

2. **添加新功能**
   - 在 `src/main.js` 中添加主进程逻辑
   - 使用 IPC 通信在主进程和渲染进程间传递数据

3. **添加菜单**
   ```javascript
   // 在 main.js 中添加
   const { Menu } = require('electron');
   
   const template = [
     {
       label: '文件',
       submenu: [
         { label: '新建', accelerator: 'CmdOrCtrl+N' },
         { label: '打开', accelerator: 'CmdOrCtrl+O' },
         { type: 'separator' },
         { label: '退出', role: 'quit' }
       ]
     }
   ];
   
   const menu = Menu.buildFromTemplate(template);
   Menu.setApplicationMenu(menu);
   ```

## 🚨 常见错误解决

### 1. npm install 失败
```bash
# 清除缓存
npm cache clean --force
# 删除 node_modules
rm -rf node_modules
# 重新安装
npm install
```

### 2. 构建失败
- 检查 Node.js 版本是否为 18+
- 确保所有图标文件都存在
- 检查 package.json 配置是否正确

### 3. 自动更新不工作
- 确保版本号格式为 v1.0.0
- 检查 GitHub 仓库是否为公开
- 确保 GitHub Actions 构建成功

### 4. 图标不显示
- 确保图标文件名正确
- 检查图标文件格式和尺寸
- 重新构建应用

## 📞 获取帮助

如果遇到问题：
1. 查看 GitHub Actions 的构建日志
2. 检查浏览器开发者工具的控制台
3. 参考 [Electron 官方文档](https://www.electronjs.org/docs)
4. 搜索相关错误信息

## 🎉 恭喜！

现在你已经有了一个完整的跨平台 Electron 应用，具备：
- ✅ 自动构建
- ✅ 自动发布
- ✅ 自动更新
- ✅ 跨平台支持

享受你的开发之旅吧！🚀
