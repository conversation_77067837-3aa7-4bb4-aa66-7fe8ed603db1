#!/usr/bin/env node

/**
 * Electron App Setup Script
 * 这个脚本帮助用户快速配置 Electron 应用
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function main() {
  console.log('🚀 欢迎使用 Electron 应用配置向导！\n');
  
  try {
    // 读取当前的 package.json
    const packagePath = path.join(__dirname, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    console.log('请提供以下信息来配置你的应用：\n');
    
    // 收集用户信息
    const appName = await question('应用名称 (例如: my-awesome-app): ');
    const appDisplayName = await question('应用显示名称 (例如: My Awesome App): ');
    const appDescription = await question('应用描述: ');
    const authorName = await question('作者姓名: ');
    const authorEmail = await question('作者邮箱: ');
    const githubUsername = await question('GitHub 用户名: ');
    const repoName = await question('GitHub 仓库名: ');
    const companyName = await question('公司名称 (例如: mycompany): ');
    
    // 更新 package.json
    packageJson.name = appName || packageJson.name;
    packageJson.description = appDescription || packageJson.description;
    packageJson.homepage = `https://github.com/${githubUsername}/${repoName}`;
    packageJson.author = {
      name: authorName || packageJson.author.name,
      email: authorEmail || packageJson.author.email
    };
    packageJson.build.appId = `com.${companyName}.${appName.replace(/[^a-zA-Z0-9]/g, '')}`;
    packageJson.build.productName = appDisplayName || appName;
    packageJson.build.publish.owner = githubUsername;
    packageJson.build.publish.repo = repoName;
    
    // 写入更新后的 package.json
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
    
    // 更新 HTML 标题
    const htmlPath = path.join(__dirname, 'src', 'index.html');
    let htmlContent = fs.readFileSync(htmlPath, 'utf8');
    htmlContent = htmlContent.replace(/<title>.*<\/title>/, `<title>${appDisplayName || appName}</title>`);
    htmlContent = htmlContent.replace(/<h1>.*<\/h1>/, `<h1>🚀 ${appDisplayName || appName}</h1>`);
    fs.writeFileSync(htmlPath, htmlContent);
    
    console.log('\n✅ 配置完成！\n');
    console.log('📋 配置摘要:');
    console.log(`   应用名称: ${appName}`);
    console.log(`   显示名称: ${appDisplayName}`);
    console.log(`   描述: ${appDescription}`);
    console.log(`   作者: ${authorName} <${authorEmail}>`);
    console.log(`   GitHub: https://github.com/${githubUsername}/${repoName}`);
    console.log(`   App ID: ${packageJson.build.appId}\n`);
    
    console.log('🎯 下一步操作:');
    console.log('1. 运行 "npm install" 安装依赖');
    console.log('2. 运行 "npm run dev" 启动开发版本');
    console.log('3. 在 assets 文件夹中添加应用图标');
    console.log('4. 将代码推送到 GitHub 仓库');
    console.log('5. 创建版本标签来触发自动构建\n');
    
    console.log('📚 详细说明请查看 USAGE_GUIDE.md 文件');
    
  } catch (error) {
    console.error('❌ 配置过程中出现错误:', error.message);
  } finally {
    rl.close();
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
